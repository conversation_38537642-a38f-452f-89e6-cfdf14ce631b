from crewai import Agent, Crew, Process, Task, LLM
from crewai.project import CrewB<PERSON>, agent, crew, task
import os
from dotenv import load_dotenv
from pydantic import BaseModel
from typing import List
import datetime

load_dotenv()
ARK_API_KEY = os.getenv("ARK_API_KEY")
ARK_BASE_URL = os.getenv("ARK_BASE_URL")
# MODEL = "openai/doubao-1-5-thinking-pro-250415"
MODEL = "openai/deepseek-r1-250528"

llm_ark = LLM(
    model=MODEL,
    api_key=ARK_API_KEY,
    base_url=ARK_BASE_URL,
)


@CrewBase
class LLPSCurator:
    """
    Creating All-in-one Curator crew
    params:
            pmid: str, paper PMID for file storage
    """

    agents_config = "../config/agents.yaml"
    tasks_config = "../config/all-in-one-task.yaml"

    def __init__(self, pmid: str):
        super().__init__()
        self.pmid = pmid

    @agent
    def llps_curator(self) -> Agent:
        return Agent(
            config=self.agents_config["llps_curator"],
            verbose=False,
            llm=llm_ark,
        )

    @agent
    def data_formatter_agent(self) -> Agent:
        return Agent(
            config=self.agents_config["data_formatter_agent"],
            verbose=False,
            llm=llm_ark,
        )

    @task
    def curation_task(self) -> Task:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        return Task(
            config=self.tasks_config["curation_task"],
            agent=self.llps_curator(),
            output_file=f"output/{self.pmid}_llps_curated_{timestamp}.md",
        )

    @task
    def formatting_task(self) -> Task:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        return Task(
            config=self.tasks_config["formatting_task"],
            agent=self.data_formatter_agent(),
            context=[self.curation_task()],
            output_file=f"output/{self.pmid}_llps_formatted_{timestamp}.json",
        )

    @crew
    def crew(self) -> Crew:
        """Creates the All-in-one Curator crew"""

        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=False,
        )


if __name__ == "__main__":
    # 获取所有PMID目录
    markdown_dir = "data/input/markdown"
    pmid_dirs = [
        d
        for d in os.listdir(markdown_dir)
        if os.path.isdir(os.path.join(markdown_dir, d)) and d.isdigit()
    ]
    pmid_dirs.sort()

    total = len(pmid_dirs)
    success = 0
    failed = 0

    print(f"开始处理 {total} 个文件...")

    for i, pmid in enumerate(pmid_dirs, 1):
        try:
            print(f"[{i}/{total}] 处理 PMID: {pmid}")
            full_text = open(
                f"data/input/markdown/{pmid}/{pmid}.md", "r", encoding="utf-8"
            ).read()
            inputs = {"paper_full_text": full_text}
            result = LLPSCurator(pmid=pmid).crew().kickoff(inputs=inputs)
            success += 1
            print(f"✅ 成功处理: {pmid}")
        except Exception as e:
            failed += 1
            print(f"❌ 处理失败: {pmid} - {str(e)}")

    print(f"\n处理完成！总计: {total}, 成功: {success}, 失败: {failed}")
