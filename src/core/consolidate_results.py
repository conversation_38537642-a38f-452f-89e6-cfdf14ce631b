import os
import json
import pandas as pd
from datetime import datetime
import re


class ResultsConsolidator:
    def __init__(self, output_dir="output"):
        self.output_dir = output_dir
        self.consolidated_data = []
        self.processing_stats = {
            "total_files": 0,
            "successfully_processed": 0,
            "failed_files": [],
            "duplicate_pmids": [],
        }

    def extract_pmid_from_filename(self, filename):
        """Extract PMID from filename like '16263761_llps_formatted_20250530_224957.json'"""
        match = re.match(r"^(\d+)_llps_formatted_", filename)
        return match.group(1) if match else None

    def get_latest_file_for_pmid(self, pmid):
        """Get the most recent JSON file for a given PMID"""
        pattern = f"{pmid}_llps_formatted_"
        matching_files = [
            f
            for f in os.listdir(self.output_dir)
            if f.startswith(pattern) and f.endswith(".json")
        ]

        if not matching_files:
            return None

        # Sort by timestamp (filename contains timestamp)
        matching_files.sort(reverse=True)
        return matching_files[0]

    def load_json_file(self, filepath):
        """Load and parse JSON file"""
        try:
            with open(filepath, "r", encoding="utf-8") as f:
                data = json.load(f)
            return data
        except Exception as e:
            print(f"Error loading {filepath}: {str(e)}")
            return None

    def flatten_experiment_data(self, experiments_list):
        """Flatten the detailed experiments list for easier analysis"""
        if not experiments_list:
            return {}

        experiment_types = [
            exp.get("experiment_type_original", "") for exp in experiments_list
        ]
        constructs = [exp.get("constructs_used", "") for exp in experiments_list]
        cell_lines = [
            exp.get("cell_lines_organism_system", "") for exp in experiments_list
        ]

        return {
            "experiment_types": "; ".join(experiment_types),
            "constructs_used": "; ".join(constructs),
            "cell_lines_used": "; ".join(cell_lines),
            "num_experiments": len(experiments_list),
        }

    def process_single_entry(self, data, pmid, filename):
        """Process a single JSON entry and extract key information"""
        if not data or not isinstance(data, list) or len(data) == 0:
            return None

        # Take the first entry if it's a list
        entry = data[0] if isinstance(data, list) else data

        # Flatten experiment data
        exp_data = self.flatten_experiment_data(
            entry.get("detailed_experiments_list", [])
        )

        # Create consolidated record
        record = {
            "pmid": pmid,
            "filename": filename,
            "gene_name": entry.get("gene_name", ""),
            "organism": entry.get("organism", ""),
            "mlo_association_summary": entry.get("mlo_association_summary", ""),
            "location": "; ".join(entry.get("location", []))
            if entry.get("location")
            else "",
            "observed_ps_behavior_summary": entry.get(
                "observed_ps_behavior_summary", ""
            ),
            "key_protein_regions_studied_ps": entry.get(
                "key_protein_regions_studied_ps", ""
            ),
            "experiment_v2_types": "; ".join(entry.get("experiment_v2_types", []))
            if entry.get("experiment_v2_types")
            else "",
            "overall_material_properties_summary": entry.get(
                "overall_material_properties_summary", ""
            ),
            "material_state_v2": "; ".join(entry.get("material_state_v2", []))
            if entry.get("material_state_v2")
            else "",
            "droplet_state_transition_observed_summary": entry.get(
                "droplet_state_transition_observed_summary", ""
            ),
            "key_drivers_intrinsic_regulators_summary": entry.get(
                "key_drivers_intrinsic_regulators_summary", ""
            ),
            "co_phase_separation_partners_extrinsic_regulators_summary": entry.get(
                "co_phase_separation_partners_extrinsic_regulators_summary", ""
            ),
            "functional_implications_summary": entry.get(
                "functional_implications_summary", ""
            ),
            "key_supporting_statements": "; ".join(
                entry.get("key_supporting_statements", [])
            )
            if entry.get("key_supporting_statements")
            else "",
            "class": entry.get("class", ""),
            "num_experiments": exp_data.get("num_experiments", 0),
            "experiment_types": exp_data.get("experiment_types", ""),
            "constructs_used": exp_data.get("constructs_used", ""),
            "cell_lines_used": exp_data.get("cell_lines_used", ""),
        }

        return record

    def consolidate_all_results(self):
        """Process all JSON files in the output directory"""
        if not os.path.exists(self.output_dir):
            print(f"Output directory {self.output_dir} not found!")
            return

        # Get all JSON files
        json_files = [
            f
            for f in os.listdir(self.output_dir)
            if "_llps_formatted_" in f and f.endswith(".json")
        ]

        self.processing_stats["total_files"] = len(json_files)
        print(f"Found {len(json_files)} JSON result files")

        # Group by PMID and get latest file for each
        pmid_to_file = {}
        for filename in json_files:
            pmid = self.extract_pmid_from_filename(filename)
            if pmid:
                if pmid in pmid_to_file:
                    self.processing_stats["duplicate_pmids"].append(pmid)
                    # Keep the more recent file
                    if filename > pmid_to_file[pmid]:
                        pmid_to_file[pmid] = filename
                else:
                    pmid_to_file[pmid] = filename

        print(f"Processing {len(pmid_to_file)} unique PMIDs")
        if self.processing_stats["duplicate_pmids"]:
            print(
                f"Found duplicates for {len(set(self.processing_stats['duplicate_pmids']))} PMIDs"
            )

        # Process each file
        for pmid, filename in pmid_to_file.items():
            filepath = os.path.join(self.output_dir, filename)
            print(f"Processing {pmid}: {filename}")

            data = self.load_json_file(filepath)
            if data is None:
                self.processing_stats["failed_files"].append(filename)
                continue

            record = self.process_single_entry(data, pmid, filename)
            if record:
                self.consolidated_data.append(record)
                self.processing_stats["successfully_processed"] += 1
            else:
                self.processing_stats["failed_files"].append(filename)

        print(f"\nProcessing complete!")
        print(
            f"Successfully processed: {self.processing_stats['successfully_processed']}"
        )
        print(f"Failed: {len(self.processing_stats['failed_files'])}")

        return self.consolidated_data

    def save_consolidated_results(self, output_filename=None):
        """Save consolidated results to CSV and Excel files"""
        if not self.consolidated_data:
            print("No data to save!")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if output_filename is None:
            base_filename = f"consolidated_llps_results_{timestamp}"
        else:
            base_filename = output_filename

        # Create DataFrame
        df = pd.DataFrame(self.consolidated_data)

        # Save as CSV
        csv_filename = f"{base_filename}.csv"
        df.to_csv(csv_filename, index=False, encoding="utf-8")
        print(f"Saved CSV: {csv_filename}")

        # Save as Excel
        excel_filename = f"{base_filename}.xlsx"
        df.to_excel(excel_filename, index=False, engine="openpyxl")
        print(f"Saved Excel: {excel_filename}")

        # Save processing stats
        stats_filename = f"{base_filename}_stats.json"
        with open(stats_filename, "w", encoding="utf-8") as f:
            json.dump(self.processing_stats, f, indent=2, ensure_ascii=False)
        print(f"Saved stats: {stats_filename}")

        return csv_filename, excel_filename, stats_filename

    def print_summary_statistics(self):
        """Print summary statistics of the consolidated data"""
        if not self.consolidated_data:
            print("No data available for statistics!")
            return

        df = pd.DataFrame(self.consolidated_data)

        print("\n" + "=" * 60)
        print("CONSOLIDATED RESULTS SUMMARY")
        print("=" * 60)

        print(f"Total records: {len(df)}")
        print(f"Unique PMIDs: {df['pmid'].nunique()}")
        print(f"Unique genes: {df['gene_name'].nunique()}")

        print(f"\nTop 10 organisms:")
        organism_counts = df["organism"].value_counts().head(10)
        for org, count in organism_counts.items():
            print(f"  {org[:50]}{'...' if len(org) > 50 else ''}: {count}")

        print(f"\nMaterial states:")
        material_states = df["material_state_v2"].value_counts()
        for state, count in material_states.items():
            print(f"  {state}: {count}")

        print(f"\nClass distribution:")
        class_dist = df["class"].value_counts()
        for cls, count in class_dist.items():
            print(f"  {cls}: {count}")

        print(f"\nExperiment types (top 10):")
        exp_types = df["experiment_v2_types"].value_counts().head(10)
        for exp_type, count in exp_types.items():
            print(f"  {exp_type}: {count}")


if __name__ == "__main__":
    consolidator = ResultsConsolidator()

    # Process all results
    data = consolidator.consolidate_all_results()

    # Save consolidated results
    if data:
        csv_file, excel_file, stats_file = consolidator.save_consolidated_results()

        # Print summary statistics
        consolidator.print_summary_statistics()

        print(f"\n📁 Files created:")
        print(f"  📊 {csv_file}")
        print(f"  📈 {excel_file}")
        print(f"  📋 {stats_file}")
    else:
        print("No data was successfully processed!")
