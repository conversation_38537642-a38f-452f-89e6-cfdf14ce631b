import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys
import importlib.util

# 动态导入带连字符的模块
spec = importlib.util.spec_from_file_location(
    "all_in_one_curator", "crews/all-in-one-curator.py"
)
all_in_one_curator = importlib.util.module_from_spec(spec)
spec.loader.exec_module(all_in_one_curator)
LLPSCurator = all_in_one_curator.LLPSCurator


class BatchProcessor:
    def __init__(self, max_workers=5):
        self.max_workers = max_workers
        self.results_lock = threading.Lock()
        self.success_count = 0
        self.failed_count = 0
        self.total_count = 0

    def process_single_pmid(self, pmid, index):
        """处理单个PMID"""
        try:
            print(
                f"[{index}/{self.total_count}] 开始处理 PMID: {pmid} (线程: {threading.current_thread().name})"
            )

            # 读取文件
            markdown_path = f"data/input/markdown/{pmid}/{pmid}.md"
            if not os.path.exists(markdown_path):
                raise FileNotFoundError(f"文件不存在: {markdown_path}")

            with open(markdown_path, "r", encoding="utf-8") as f:
                full_text = f.read()

            # 处理
            inputs = {"paper_full_text": full_text}
            result = LLPSCurator(pmid=pmid).crew().kickoff(inputs=inputs)

            # 更新成功计数
            with self.results_lock:
                self.success_count += 1
                print(
                    f"✅ [{index}/{self.total_count}] 成功处理: {pmid} (成功: {self.success_count}, 失败: {self.failed_count})"
                )

            return {"pmid": pmid, "status": "success", "result": result}

        except Exception as e:
            # 更新失败计数
            with self.results_lock:
                self.failed_count += 1
                print(
                    f"❌ [{index}/{self.total_count}] 处理失败: {pmid} - {str(e)} (成功: {self.success_count}, 失败: {self.failed_count})"
                )

            return {"pmid": pmid, "status": "failed", "error": str(e)}

    def run_batch(self):
        """运行批处理"""
        # 获取所有PMID目录
        markdown_dir = "data/input/markdown"
        if not os.path.exists(markdown_dir):
            print(f"❌ 目录不存在: {markdown_dir}")
            return

        pmid_dirs = [
            d
            for d in os.listdir(markdown_dir)
            if os.path.isdir(os.path.join(markdown_dir, d)) and d.isdigit()
        ]
        pmid_dirs.sort()

        self.total_count = len(pmid_dirs)
        print(f"🚀 开始并发处理 {self.total_count} 个文件，并发数: {self.max_workers}")
        print("=" * 60)

        start_time = time.time()

        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_pmid = {
                executor.submit(self.process_single_pmid, pmid, i + 1): pmid
                for i, pmid in enumerate(pmid_dirs)
            }

            # 等待所有任务完成
            for future in as_completed(future_to_pmid):
                pmid = future_to_pmid[future]
                try:
                    future.result()  # 获取结果，但不需要存储
                except Exception as e:
                    with self.results_lock:
                        self.failed_count += 1
                        print(f"❌ 线程异常 PMID {pmid}: {str(e)}")

        end_time = time.time()
        elapsed_time = end_time - start_time

        print("\n" + "=" * 60)
        print("🎉 批处理完成！")
        print(f"📊 总计: {self.total_count}")
        print(f"✅ 成功: {self.success_count}")
        print(f"❌ 失败: {self.failed_count}")
        print(f"⏱️  总耗时: {elapsed_time:.2f} 秒")
        print(f"📈 平均每个文件: {elapsed_time / self.total_count:.2f} 秒")
        print("=" * 60)


if __name__ == "__main__":
    # 可以通过命令行参数调整并发数
    import sys

    max_workers = 5  # 默认并发数
    if len(sys.argv) > 1:
        try:
            max_workers = int(sys.argv[1])
            print(f"设置并发数为: {max_workers}")
        except ValueError:
            print("并发数必须是整数，使用默认值 5")

    processor = BatchProcessor(max_workers=max_workers)
    processor.run_batch()
