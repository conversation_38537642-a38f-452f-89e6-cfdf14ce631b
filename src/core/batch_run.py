import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from crews.all_in_one_curator import LLPSCurator


class BatchProcessor:
    def __init__(self, max_workers=5):
        self.max_workers = max_workers
        self.results_lock = threading.Lock()
        self.success_count = 0
        self.failed_count = 0
        self.total_count = 0

    def process_single_pmid(self, pmid, index):
        """Process a single PMID"""
        try:
            print(
                f"[{index}/{self.total_count}] Processing PMID: {pmid} (Thread: {threading.current_thread().name})"
            )

            # Read file
            markdown_path = f"data/input/markdown/{pmid}/{pmid}.md"
            if not os.path.exists(markdown_path):
                raise FileNotFoundError(f"File not found: {markdown_path}")

            with open(markdown_path, "r", encoding="utf-8") as f:
                full_text = f.read()

            # Process
            inputs = {"paper_full_text": full_text}
            result = LLPSCurator(pmid=pmid).crew().kickoff(inputs=inputs)

            # Update success count
            with self.results_lock:
                self.success_count += 1
                print(
                    f"✅ [{index}/{self.total_count}] Successfully processed: {pmid} (Success: {self.success_count}, Failed: {self.failed_count})"
                )

            return {"pmid": pmid, "status": "success", "result": result}

        except Exception as e:
            # Update failure count
            with self.results_lock:
                self.failed_count += 1
                print(
                    f"❌ [{index}/{self.total_count}] Failed to process: {pmid} - {str(e)} (Success: {self.success_count}, Failed: {self.failed_count})"
                )

            return {"pmid": pmid, "status": "failed", "error": str(e)}

    def run_batch(self):
        """Run batch processing"""
        # Get all PMID directories
        markdown_dir = "data/input/markdown"
        if not os.path.exists(markdown_dir):
            print(f"❌ Directory not found: {markdown_dir}")
            return

        pmid_dirs = [
            d
            for d in os.listdir(markdown_dir)
            if os.path.isdir(os.path.join(markdown_dir, d)) and d.isdigit()
        ]
        pmid_dirs.sort()

        self.total_count = len(pmid_dirs)
        print(
            f"🚀 Starting concurrent processing of {self.total_count} files with {self.max_workers} workers"
        )
        print("=" * 60)

        start_time = time.time()

        # Use thread pool for concurrent processing
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_pmid = {
                executor.submit(self.process_single_pmid, pmid, i + 1): pmid
                for i, pmid in enumerate(pmid_dirs)
            }

            # Wait for all tasks to complete
            for future in as_completed(future_to_pmid):
                pmid = future_to_pmid[future]
                try:
                    future.result()  # Get result but don't need to store
                except Exception as e:
                    with self.results_lock:
                        self.failed_count += 1
                        print(f"❌ Thread exception for PMID {pmid}: {str(e)}")

        end_time = time.time()
        elapsed_time = end_time - start_time

        print("\n" + "=" * 60)
        print("🎉 Batch processing completed!")
        print(f"📊 Total: {self.total_count}")
        print(f"✅ Success: {self.success_count}")
        print(f"❌ Failed: {self.failed_count}")
        print(f"⏱️  Total time: {elapsed_time:.2f} seconds")
        print(f"📈 Average per file: {elapsed_time / self.total_count:.2f} seconds")
        print("=" * 60)


if __name__ == "__main__":
    # Can adjust concurrency via command line argument
    import sys

    max_workers = 5  # Default concurrency
    if len(sys.argv) > 1:
        try:
            max_workers = int(sys.argv[1])
            print(f"Setting max workers to: {max_workers}")
        except ValueError:
            print("Max workers must be an integer, using default value 5")

    processor = BatchProcessor(max_workers=max_workers)
    processor.run_batch()
