#!/bin/bash

# Batch processing script for LLPS Curator
# This script processes all markdown files in data/input/markdown directory

echo "🚀 Starting LLPS Curator Batch Processing"
echo "=========================================="

# Check if we're in the right directory
if [ ! -d "data/input/markdown" ]; then
    echo "❌ Error: data/input/markdown directory not found!"
    echo "Please run this script from the project root directory."
    exit 1
fi

# Check if output directory exists, create if not
if [ ! -d "output" ]; then
    echo "📁 Creating output directory..."
    mkdir -p output
fi

# Count total files to process
total_files=$(find data/input/markdown -name "*.md" -type f | wc -l)
echo "📊 Found $total_files markdown files to process"

# Run the Python script
echo "🔄 Starting processing..."
cd src/core/crews
python all-in-one-curator.py --all

echo ""
echo "✅ Batch processing completed!"
echo "📁 Check the 'output' directory for results"
echo "📄 Each PMID will have both .md (curated) and .json (formatted) files"
